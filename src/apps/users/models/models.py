from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from django.core.validators import RegexValidator
import uuid

class User(AbstractUser):
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    image_url = models.CharField(max_length=200, null=True, blank=True)
    phone_number = models.CharField(
        validators=[phone_regex],
        max_length=17,
        null=False,
        blank=False,
        help_text="Phone number in international format"
    )
    date_deleted = models.DateTimeField(null=True, blank=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'users'
        db_table = 'users_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.username} ({self.email})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def is_deleted(self):
        return self.date_deleted is not None


    @classmethod
    def create_user(cls,username,email,password):
        user = cls(username=username,email=email)
        user.set_password(password)
        return user

    def modify_user(self, **kwargs):
        allowed_fields = [
            'first_name', 'last_name', 'username', 'email',
            'phone_number', 'image_url'
        ]
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(self, field):
                setattr(self, field, value)
            elif field not in allowed_fields:
                raise ValueError(f"Field '{field}' is not allowed to be modified")
        self.date_updated = timezone.now()
        return self

    
    def soft_delete(self):
        if self.date_deleted is not None:
            raise ValueError("User is already deleted")
        self.date_deleted = timezone.now()
        self.is_active = False 
        self.save()
        return self

    def restore(self):
        if self.date_deleted is None:
            raise ValueError("User is not deleted")

        self.date_deleted = None
        self.is_active = True
        return self

    def hard_delete(self):
        super().delete()


class Group(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True, help_text="Group name")
    description = models.TextField(blank=True, null=True, help_text="Group description")
    created_by = models.ForeignKey(
        'User',
        on_delete=models.CASCADE,
        related_name='created_groups',
        help_text="User who created this group"
    )
    members = models.ManyToManyField(
        'User',
        through='GroupMembership',
        related_name='user_groups',
        help_text="Users who are members of this group"
    )
    is_active = models.BooleanField(default=True, help_text="Whether the group is active")
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    date_deleted = models.DateTimeField(null=True, blank=True)

    class Meta:
        app_label = 'users'
        db_table = 'users_group'
        verbose_name = 'Group'
        verbose_name_plural = 'Groups'
        ordering = ['-date_created']

    def __str__(self):
        return f"{self.name}"

    @property
    def is_deleted(self):
        return self.date_deleted is not None

    @property
    def member_count(self):
        return self.members.filter(groupmembership__is_active=True).count()

    def add_member(self, user, role='member'):
        """Add a user to the group with specified role"""
        membership, created = GroupMembership.objects.get_or_create(
            group=self,
            user=user,
            defaults={'role': role, 'is_active': True}
        )
        if not created and not membership.is_active:
            membership.is_active = True
            membership.role = role
            membership.save()
        return membership

    def remove_member(self, user):
        """Remove a user from the group"""
        try:
            membership = GroupMembership.objects.get(group=self, user=user)
            membership.is_active = False
            membership.save()
            return True
        except GroupMembership.DoesNotExist:
            return False

    def soft_delete(self):
        """Soft delete the group"""
        if self.date_deleted is not None:
            raise ValueError("Group is already deleted")
        self.date_deleted = timezone.now()
        self.is_active = False
        self.save()
        return self

    def restore(self):
        """Restore a soft deleted group"""
        if self.date_deleted is None:
            raise ValueError("Group is not deleted")
        self.date_deleted = None
        self.is_active = True
        self.save()
        return self

    def hard_delete(self):
        """Permanently delete the group"""
        super().delete()


class GroupMembership(models.Model):
    ROLE_CHOICES = [
        ('admin', 'Admin'),
        ('moderator', 'Moderator'),
        ('member', 'Member'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    group = models.ForeignKey(Group, on_delete=models.CASCADE)
    user = models.ForeignKey('User', on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='member')
    is_active = models.BooleanField(default=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'users'
        db_table = 'users_group_membership'
        verbose_name = 'Group Membership'
        verbose_name_plural = 'Group Memberships'
        unique_together = ['group', 'user']
        ordering = ['-date_joined']

    def __str__(self):
        return f"{self.user.username} in {self.group.name} as {self.role}"