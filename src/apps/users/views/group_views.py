import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from ..models import Group, GroupMembership
from ..exceptions.exceptions import (
    GroupException,
    GroupNotFoundException,
    GroupPermissionException,
    GroupMembershipException
)
from ..serializers.group_serializers import (
    GroupCreateSerializer,
    GroupUpdateSerializer,
    GroupListSerializer,
    GroupDetailSerializer,
    GroupMemberAddSerializer,
    GroupMemberUpdateSerializer,
    GroupMemberRemoveSerializer
)
from ..services.group_service import GroupService

User = get_user_model()
logger = logging.getLogger(__name__)


class CreateGroupView(generics.CreateAPIView):
    parser_classes = (JSONParser,)
    queryset = Group.objects.all()
    serializer_class = GroupCreateSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        group = serializer.save()
        headers = self.get_success_headers(serializer.data)

        logger.info(f"Group created successfully: {group.name}")

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class UpdateGroupView(generics.UpdateAPIView):
    parser_classes = (JSONParser,)
    serializer_class = GroupUpdateSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        group_id = self.kwargs.get('id')
        return GroupService.get_group_by_id(group_id)

    def put(self, request, *args, **kwargs):
        group = self.get_object()
        serializer = self.get_serializer(group, data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        updated_group = GroupService.update_group(
            group_id=group.id,
            name=validated_data.get('name'),
            description=validated_data.get('description'),
            user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )

        response_serializer = self.get_serializer(updated_group)
        logger.info(f"Group updated successfully: {updated_group.name}")

        return Response(
            response_serializer.data,
            status=status.HTTP_200_OK
        )


class GetAllGroupsView(generics.ListAPIView):
    serializer_class = GroupListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        include_inactive = self.request.query_params.get('include_inactive', 'false').lower() == 'true'
        return GroupService.get_all_groups(include_inactive=include_inactive)


    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            logger.info(f"Retrieved {len(serializer.data)} groups")

            return Response({
                'count': len(serializer.data),
                'groups': serializer.data
            }, status=status.HTTP_200_OK)

        except GroupException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting all groups: {str(e)}")
            raise GroupException(
                message="An unexpected error occurred while retrieving groups",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    pagination_class = None


class GetGroupByIdView(generics.RetrieveAPIView):
    serializer_class = GroupDetailSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        group_id = self.kwargs.get('id')
        return GroupService.get_group_by_id(group_id)

    def get(self, request, *args, **kwargs):
        group = self.get_object()
        serializer = self.get_serializer(group)
        return Response({
            'group': serializer.data
        }, status=status.HTTP_200_OK)


class DeleteGroupView(generics.DestroyAPIView):
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        group_id = self.kwargs.get('id')
        return GroupService.get_group_by_id(group_id)

    def delete(self, request, *args, **kwargs):
        group = self.get_object()
        GroupService.delete_group(
            group_id=group.id,
            user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )
        
        logger.info(f"Group deleted successfully: {group.name}")
        return Response(status=status.HTTP_204_NO_CONTENT)


class GetUserGroupsView(generics.ListAPIView):
    serializer_class = GroupListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        user_id = self.kwargs.get('user_id')
        return GroupService.get_user_groups(user_id)

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            user_id = self.kwargs.get('user_id')
            logger.info(f"Retrieved {len(serializer.data)} groups for user {user_id}")

            return Response({
                'count': len(serializer.data),
                'groups': serializer.data
            }, status=status.HTTP_200_OK)

        except GroupException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting user groups: {str(e)}")
            raise GroupException(
                message="An unexpected error occurred while retrieving user groups",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    pagination_class = None
