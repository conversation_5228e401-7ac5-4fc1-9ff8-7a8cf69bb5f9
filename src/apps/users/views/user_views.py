import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONPars<PERSON>
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from ..exceptions.exceptions import UserException
from ..serializers.user_serializers import UserRegisterSerializer,UserModifySerializer,GetAllUsersSerializer,UserDetailSerializer
from ..services.user_service import UserService

User = get_user_model()
logger = logging.getLogger(__name__)


class RegisterView(generics.CreateAPIView):
    parser_classes = (J<PERSON>NPars<PERSON>, FormParser, MultiPartParser)
    queryset = User.objects.all()
    serializer_class = UserRegisterSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.save()
        headers = self.get_success_headers(serializer.data)


        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class ModifyView(generics.GenericAPIView):
    parser_classes = (FormParser, MultiPartParser, JSONParser)
    queryset = User.objects.all()
    serializer_class = UserModifySerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data

        user = UserService.modify_user(
            user_id=validated_data['id'],
            first_name=validated_data['first_name'],
            last_name=validated_data['last_name'],
            username=validated_data['username'],
            email=validated_data['email'],
            image_modified=validated_data.get('image_modifided', False),
            image=validated_data.get('image')
        )

        response_serializer = self.get_serializer(user)

        logger.info(f"User modified successfully: {user.username}")

        return Response(
            response_serializer.data,
            status=status.HTTP_200_OK
        )


class GetAllUsersView(generics.ListAPIView):
    serializer_class = GetAllUsersSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        include_inactive = self.request.query_params.get('include_inactive', 'false').lower() == 'true'
        return UserService.get_all_users(include_inactive=include_inactive)

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            logger.info(f"Retrieved {len(serializer.data)} users")

            return Response({
                'count': len(serializer.data),
                'users': serializer.data
            }, status=status.HTTP_200_OK)

        except UserException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting all users: {str(e)}")
            raise UserException(
                message="An unexpected error occurred while retrieving users",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    pagination_class = None


class GetUserByIdView(generics.RetrieveAPIView):
    serializer_class = UserDetailSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        user_id = self.kwargs.get('id')
        return UserService.get_user_by_id(user_id)

    def get(self, request, *args, **kwargs):
        user = self.get_object()
        serializer = self.get_serializer(user)
        return Response({
            'user': serializer.data
        }, status=status.HTTP_200_OK)

