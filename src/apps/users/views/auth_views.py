import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import Allow<PERSON>ny, IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError
from django.contrib.auth import get_user_model, authenticate
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from rest_framework import serializers
from ..exceptions.exceptions import UserException

User = get_user_model()
logger = logging.getLogger(__name__)


class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(
        help_text="Username or email address"
    )
    password = serializers.CharField(
        write_only=True,
        help_text="User password"
    )

    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')

        if username and password:
            user = authenticate(username=username, password=password)

            if not user:
                try:
                    user_obj = User.objects.get(email=username)
                    user = authenticate(username=user_obj.username, password=password)
                except User.DoesNotExist:
                    pass

            if not user:
                raise serializers.ValidationError('Invalid credentials')

            if not user.is_active:
                raise serializers.ValidationError('User account is disabled')

            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('Must include username and password')


class TokenRefreshSerializer(serializers.Serializer):
    refresh = serializers.CharField(
        help_text="Refresh token"
    )


class LogoutSerializer(serializers.Serializer):
    refresh = serializers.CharField(
        help_text="Refresh token to blacklist"
    )


class VerifyTokenSerializer(serializers.Serializer):
    pass


class LoginView(generics.GenericAPIView):
    serializer_class = LoginSerializer
    permission_classes = [AllowAny]
    parser_classes = [JSONParser]
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.validated_data['user']
        refresh = RefreshToken.for_user(user)

        logger.info(f"User logged in successfully: {user.username}")

        return Response({
            'access_token': str(refresh.access_token),
            'refresh_token': str(refresh),
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': user.full_name,
                'image_url': user.image_url
            }
        }, status=status.HTTP_200_OK)


class TokenRefreshView(generics.GenericAPIView):
    serializer_class = TokenRefreshSerializer
    permission_classes = [AllowAny]
    parser_classes = [JSONParser]
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            refresh_token = RefreshToken(serializer.validated_data['refresh'])
            access_token = refresh_token.access_token

            return Response({
                'access_token': str(access_token)
            }, status=status.HTTP_200_OK)

        except TokenError as e:
            logger.warning(f"Token refresh failed: {str(e)}")
            raise UserException(
                message="Invalid or expired refresh token",
                code="INVALID_TOKEN",
                status_code=status.HTTP_401_UNAUTHORIZED
            )


class LogoutView(generics.GenericAPIView):
    serializer_class = LogoutSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [JSONParser]
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            refresh_token = RefreshToken(serializer.validated_data['refresh'])
            refresh_token.blacklist()

            logger.info(f"User logged out successfully: {request.user.username}")

            return Response({
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)

        except TokenError as e:
            logger.warning(f"Logout failed: {str(e)}")
            raise UserException(
                message="Invalid refresh token",
                code="INVALID_TOKEN",
                status_code=status.HTTP_400_BAD_REQUEST
            )


class VerifyTokenView(generics.GenericAPIView):
    serializer_class = VerifyTokenSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [JSONParser]
    def get(self, request, *args, **kwargs):
        user = request.user

        return Response({
            'valid': True,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': user.full_name,
                'image_url': user.image_url,
                'is_staff': user.is_staff,
                'is_active': user.is_active
            }
        }, status=status.HTTP_200_OK)