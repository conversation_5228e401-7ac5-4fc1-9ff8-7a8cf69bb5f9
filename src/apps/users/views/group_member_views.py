import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from ..models import Group, GroupMembership
from ..exceptions.exceptions import (
    GroupException,
    GroupNotFoundException,
    GroupPermissionException,
    GroupMembershipException
)
from ..serializers.group_serializers import (
    GroupMemberAddSerializer,
    GroupMemberUpdateSerializer,
    GroupMemberRemoveSerializer,
    GroupMembershipSerializer
)
from ..services.group_service import GroupService

User = get_user_model()
logger = logging.getLogger(__name__)


class AddGroupMemberView(generics.CreateAPIView):
    parser_classes = (JSONParser,)
    serializer_class = GroupMemberAddSerializer
    permission_classes = [AllowAny]

    @extend_schema(
        summary="Add member to group",
        description="Add a user to a group with specified role (admin only)",
        request=GroupMemberAddSerializer,
        responses={
            201: OpenApiResponse(
                response=GroupMembershipSerializer,
                description="Member added successfully"
            ),
            400: OpenApiResponse(description="Bad request"),
            403: OpenApiResponse(description="Permission denied"),
            404: OpenApiResponse(description="Group or user not found")
        },
        examples=[
            OpenApiExample(
                "Add Member Example",
                value={
                    "user_id": "123e4567-e89b-12d3-a456-426614174000",
                    "role": "member"
                }
            )
        ]
    )
    def post(self, request, *args, **kwargs):
        group_id = self.kwargs.get('group_id')
        group = GroupService.get_group_by_id(group_id)
        
        serializer = self.get_serializer(
            data=request.data,
            context={'group': group}
        )
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        membership = GroupService.add_member(
            group_id=group_id,
            user_id=validated_data['user_id'].id,
            role=validated_data['role'],
            requesting_user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )

        response_serializer = GroupMembershipSerializer(membership)
        logger.info(f"Member added to group successfully")

        return Response(
            response_serializer.data,
            status=status.HTTP_201_CREATED
        )


class UpdateGroupMemberView(generics.UpdateAPIView):
    parser_classes = (JSONParser,)
    serializer_class = GroupMemberUpdateSerializer
    permission_classes = [AllowAny]

    @extend_schema(
        summary="Update member role",
        description="Update a group member's role (admin only)",
        request=GroupMemberUpdateSerializer,
        responses={
            200: OpenApiResponse(
                response=GroupMembershipSerializer,
                description="Member role updated successfully"
            ),
            403: OpenApiResponse(description="Permission denied"),
            404: OpenApiResponse(description="Group or member not found")
        },
        examples=[
            OpenApiExample(
                "Update Role Example",
                value={
                    "role": "moderator"
                }
            )
        ]
    )
    def put(self, request, *args, **kwargs):
        group_id = self.kwargs.get('group_id')
        user_id = self.kwargs.get('user_id')
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        membership = GroupService.update_member_role(
            group_id=group_id,
            user_id=user_id,
            new_role=validated_data['role'],
            requesting_user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )

        response_serializer = GroupMembershipSerializer(membership)
        logger.info(f"Member role updated successfully")

        return Response(
            response_serializer.data,
            status=status.HTTP_200_OK
        )


class RemoveGroupMemberView(generics.DestroyAPIView):
    parser_classes = (JSONParser,)
    serializer_class = GroupMemberRemoveSerializer
    permission_classes = [AllowAny]

    @extend_schema(
        summary="Remove member from group",
        description="Remove a user from a group (admin only or self-removal)",
        responses={
            204: OpenApiResponse(description="Member removed successfully"),
            403: OpenApiResponse(description="Permission denied"),
            404: OpenApiResponse(description="Group or member not found")
        }
    )
    def delete(self, request, *args, **kwargs):
        group_id = self.kwargs.get('group_id')
        user_id = self.kwargs.get('user_id')
        
        GroupService.remove_member(
            group_id=group_id,
            user_id=user_id,
            requesting_user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )

        logger.info(f"Member removed from group successfully")
        return Response(status=status.HTTP_204_NO_CONTENT)


class RemoveGroupMemberByBodyView(generics.CreateAPIView):
    parser_classes = (JSONParser,)
    serializer_class = GroupMemberRemoveSerializer
    permission_classes = [AllowAny]

    @extend_schema(
        summary="Remove member from group (by request body)",
        description="Remove a user from a group using request body (admin only or self-removal)",
        request=GroupMemberRemoveSerializer,
        responses={
            204: OpenApiResponse(description="Member removed successfully"),
            403: OpenApiResponse(description="Permission denied"),
            404: OpenApiResponse(description="Group or member not found")
        },
        examples=[
            OpenApiExample(
                "Remove Member Example",
                value={
                    "user_id": "123e4567-e89b-12d3-a456-426614174000"
                }
            )
        ]
    )
    def post(self, request, *args, **kwargs):
        group_id = self.kwargs.get('group_id')
        group = GroupService.get_group_by_id(group_id)
        
        serializer = self.get_serializer(
            data=request.data,
            context={'group': group}
        )
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        GroupService.remove_member(
            group_id=group_id,
            user_id=validated_data['user_id'].id,
            requesting_user=request.user if hasattr(request, 'user') and request.user.is_authenticated else None
        )

        logger.info(f"Member removed from group successfully")
        return Response(status=status.HTTP_204_NO_CONTENT)


class GetGroupMembersView(generics.ListAPIView):
    serializer_class = GroupMembershipSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        group_id = self.kwargs.get('group_id')
        group = GroupService.get_group_by_id(group_id)
        return GroupMembership.objects.filter(
            group=group,
            is_active=True
        ).order_by('-date_joined')

    @extend_schema(
        summary="Get group members",
        description="Retrieve all members of a specific group",
        responses={
            200: OpenApiResponse(
                response=GroupMembershipSerializer(many=True),
                description="Group members retrieved successfully"
            ),
            404: OpenApiResponse(description="Group not found")
        }
    )
    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            group_id = self.kwargs.get('group_id')
            logger.info(f"Retrieved {len(serializer.data)} members for group {group_id}")

            return Response({
                'count': len(serializer.data),
                'members': serializer.data
            }, status=status.HTTP_200_OK)

        except GroupException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting group members: {str(e)}")
            raise GroupException(
                message="An unexpected error occurred while retrieving group members",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    pagination_class = None
