from django.urls import path
from .views.user_views import RegisterView, ModifyView, GetAllUsersView, GetUserByIdView
from .views.auth_views import LoginView, LogoutView, TokenRefreshView, VerifyTokenView
from .views.group_views import (
    CreateGroupView, UpdateGroupView, GetAllGroupsView,
    GetGroupByIdView, DeleteGroupView, GetUserGroupsView
)
from .views.group_member_views import (
    AddGroupMemberView, UpdateGroupMemberView, RemoveGroupMemberView,
    RemoveGroupMemberByBodyView, GetGroupMembersView
)

urlpatterns = [
    # User management endpoints
    path("register/", RegisterView.as_view(), name="register"),
    path("modify/", ModifyView.as_view(), name="modify"),
    path("all/", GetAllUsersView.as_view(), name="get_all_users"),
    path("<uuid:id>/", GetUserByIdView.as_view(), name="get_user_by_id"),

    # Authentication endpoints
    path("auth/login/", LoginView.as_view(), name="login"),
    path("auth/logout/", LogoutView.as_view(), name="logout"),
    path("auth/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("auth/verify/", VerifyTokenView.as_view(), name="verify_token"),

    # Group management endpoints
    path("groups/", CreateGroupView.as_view(), name="create_group"),
    path("groups/all/", GetAllGroupsView.as_view(), name="get_all_groups"),
    path("groups/<uuid:id>/", GetGroupByIdView.as_view(), name="get_group_by_id"),
    path("groups/<uuid:id>/update/", UpdateGroupView.as_view(), name="update_group"),
    path("groups/<uuid:id>/delete/", DeleteGroupView.as_view(), name="delete_group"),
    path("users/<uuid:user_id>/groups/", GetUserGroupsView.as_view(), name="get_user_groups"),

    # Group member management endpoints
    path("groups/<uuid:group_id>/members/", GetGroupMembersView.as_view(), name="get_group_members"),
    path("groups/<uuid:group_id>/members/add/", AddGroupMemberView.as_view(), name="add_group_member"),
    path("groups/<uuid:group_id>/members/remove/", RemoveGroupMemberByBodyView.as_view(), name="remove_group_member_by_body"),
    path("groups/<uuid:group_id>/members/<uuid:user_id>/update/", UpdateGroupMemberView.as_view(), name="update_group_member"),
    path("groups/<uuid:group_id>/members/<uuid:user_id>/remove/", RemoveGroupMemberView.as_view(), name="remove_group_member"),
]
