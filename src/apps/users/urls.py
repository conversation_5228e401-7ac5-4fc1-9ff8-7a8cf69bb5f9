from django.urls import path
from .views.user_views import RegisterView, ModifyView, GetAllUsersView, GetUserByIdView
from .views.auth_views import LoginView, LogoutView, TokenRefreshView, VerifyTokenView
from .views.group_views import (
    CreateGroupView, UpdateGroupView, GetAllGroupsView,
    GetGroupByIdView, DeleteGroupView, GetUserGroupsView,
    GetAllPermissionsView, CreateGroupWithPermissionsView,
    UpdateGroupWithPermissionsView, GetAllGroupsWithPermissionsView,
    GetGroupWithPermissionsByIdView
)
from .views.group_member_views import (
    AddGroupMemberView, UpdateGroupMemberView, RemoveGroupMemberView,
    RemoveGroupMemberByBodyView, GetGroupMembersView
)

urlpatterns = [
    # User management endpoints
    path("register/", RegisterView.as_view(), name="register"),
    path("modify/", ModifyView.as_view(), name="modify"),
    path("all/", GetAllUsersView.as_view(), name="get_all_users"),
    path("<uuid:id>/", GetUserByIdView.as_view(), name="get_user_by_id"),

    # Authentication endpoints
    path("auth/login/", LoginView.as_view(), name="login"),
    path("auth/logout/", LogoutView.as_view(), name="logout"),
    path("auth/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("auth/verify/", VerifyTokenView.as_view(), name="verify_token"),

]
