from django.contrib import admin
from django.contrib.auth import get_user_model
from .models import Group, GroupMembership

User = get_user_model()


class GroupMembershipInline(admin.TabularInline):
    model = GroupMembership
    extra = 0
    fields = ('user', 'role', 'is_active', 'date_joined')
    readonly_fields = ('date_joined',)


@admin.register(Group)
class GroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_by', 'member_count', 'is_active', 'date_created')
    list_filter = ('is_active', 'date_created', 'date_updated')
    search_fields = ('name', 'description', 'created_by__username', 'created_by__email')
    readonly_fields = ('id', 'date_created', 'date_updated', 'member_count')
    inlines = [GroupMembershipInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'name', 'description', 'created_by')
        }),
        ('Status', {
            'fields': ('is_active', 'member_count')
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated', 'date_deleted'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')


@admin.register(GroupMembership)
class GroupMembershipAdmin(admin.ModelAdmin):
    list_display = ('group', 'user', 'role', 'is_active', 'date_joined')
    list_filter = ('role', 'is_active', 'date_joined')
    search_fields = ('group__name', 'user__username', 'user__email')
    readonly_fields = ('id', 'date_joined', 'date_updated')

    fieldsets = (
        ('Membership Information', {
            'fields': ('id', 'group', 'user', 'role', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('date_joined', 'date_updated'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('group', 'user')


# Register User model if not already registered
if not admin.site.is_registered(User):
    @admin.register(User)
    class UserAdmin(admin.ModelAdmin):
        list_display = ('username', 'email', 'first_name', 'last_name', 'is_active', 'date_joined')
        list_filter = ('is_active', 'is_staff', 'is_superuser', 'date_joined')
        search_fields = ('username', 'email', 'first_name', 'last_name')
        readonly_fields = ('id', 'date_joined', 'last_login', 'date_updated')

        fieldsets = (
            ('Basic Information', {
                'fields': ('id', 'username', 'email', 'first_name', 'last_name')
            }),
            ('Contact Information', {
                'fields': ('phone_number', 'image_url')
            }),
            ('Permissions', {
                'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
                'classes': ('collapse',)
            }),
            ('Timestamps', {
                'fields': ('date_joined', 'last_login', 'date_updated', 'date_deleted'),
                'classes': ('collapse',)
            }),
        )
